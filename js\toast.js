// ✅ FILE: js/toast.js
// This file handles all toasts, visual and Telegram

// 📦 Create container only once
if (!document.getElementById("toast-container")) {
  const container = document.createElement("div");
  container.id = "toast-container";
  container.style.position = "fixed";
  container.style.bottom = "20px";
  container.style.right = "20px";
  container.style.zIndex = "9999";
  container.style.maxWidth = "350px";
  document.body.appendChild(container);
}

function showToast(type, data) {
  const toast = document.createElement("div");
  toast.classList.add("toast-box");

  // 🎨 Base colors
  toast.style.background =
    type === "error" ? "#ffdddd" :
    type === "sell" ? "#d1ecf1" :
    type === "network" ? "#fff3cd" : // light yellow for warning
    "#d4edda"; // green default for success

  toast.style.borderLeft =
    type === "error" ? "6px solid #e53935" :
    type === "sell" ? "6px solid #0c5460" :
    type === "network" ? "6px solid #b38f00" : // dark yellow for disconnection
    "6px solid #155724";

  toast.style.color = "#333";
  toast.style.padding = "10px 15px";
  toast.style.marginTop = "10px";
  toast.style.borderRadius = "8px";
  toast.style.boxShadow = "0 2px 6px rgba(0,0,0,0.2)";
  toast.style.fontSize = "14px";
  toast.style.lineHeight = "1.4";

  // ✨ Dynamic content
  let content = "";

  if (type === "buy") {
    const from = Number(data.level_from);
    const to = Number(data.level_to);

    content = `📈 Buy Order Executed<br>
• Trade ID: ${data.trade_id}<br>
• Target price: ${data.target_price}<br>
• Execution price: ${data.execution_price || data.target_price}<br>
• Average purchase price: ${data.avg_purchase_price || 'N/A'}<br>
• Expense: ${data.spesa} ${data.quote} (~${data.spesa_usd} USD)<br>
• Quantity bought: ${data.qty} ${data.base} (~${data.qty_usd} USD)<br>
• Level: ${from} → ${to}`;

    // 📲 Telegram
    sendToTelegram("buy", data);

  } else if (type === "sell") {
    content = `📉 Sell Order Executed<br>
• Trade ID: ${data.trade_id}<br>
• Target price: ${data.target_price}<br>
• Execution price: ${data.execution_price || data.target_price}<br>
• Average purchase price: ${data.avg_purchase_price || 'N/A'}<br>
• Quantity sold: ${data.qty} ${data.base} (~${data.qty_usd} USD)<br>
• Net profit: +${data.ricavo_usd} USD<br>
• 50/50 Split: ${data.usdt_half} ${data.quote} (~${data.usdt_half_usd} USD) + ${data.crypto_half} ${data.base} (~${data.crypto_half_usd} USD)`;

    // 📲 Telegram
    sendToTelegram("sell", data);

  } else if (type === "error") {
    content = `❌ <b>${data.title || "Error"}</b><br>${data.message}`;

    // 📲 Telegram
    sendToTelegram("error", {
      title: data.title || "Error",
      message: data.message
    });

  } else if (type === "network") {
    // 🚨 DISCONNECTION
    content = `⚠️ <b>Connection Lost</b><br>Emporion has lost connection to Binance.`;
    sendToTelegram("disconnected", { crypto: currentCrypto });

  } else if (type === "success" && data.title === "✅ Connection Restored") {
    // 🔌 RECONNECTION
    content = `🔌 <b>Connection Restored</b><br>Emporion has resumed connection with Binance.`;
    sendToTelegram("reconnected", { crypto: currentCrypto });

  } else {
    // 📝 GENERIC MESSAGES
    content = `<b>${data.title || "Notification"}</b><br>${data.message}`;
  }

  // 📥 Show toast on screen
  toast.innerHTML = content;
  document.getElementById("toast-container").appendChild(toast);

  // ⏳ Remove after 6 seconds
  setTimeout(() => toast.remove(), 6000);
}
