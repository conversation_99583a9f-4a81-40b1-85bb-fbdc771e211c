<?php 
include "conn.php";
include "functions.php";
session_start();

header('Content-Type: application/json');

$function = new Functions();
$jsonData = file_get_contents("php://input");
$data = json_decode($jsonData);

$dateTime = Date("m-d-Y");

// 🔵 ORDINE DI VENDITA TRAMITE API
$order = $function->sellUSDT($data->crypto, $data->future_sell_usdt, $data->actualPrice);

if (!empty($order)) {

    // ✅ Valore USDT/BTC ricevuto
    $usdtValue = ($order['cummulativeQuoteQty'] == 0) 
        ? $data->actualPrice * $order['origQty'] 
        : $order['cummulativeQuoteQty'];

    // ✅ Quantità venduta in crypto base
    $qty_sold = (float)$order['origQty'];

    // ✅ Prezzo di vendita
    $sell_price = (float)$data->actualPrice;

    // ✅ BTC/BNB ricevuti
    $btc_received = (float)$usdtValue;

    // ✅ Prendo gli asset base/quote reali (es. ENA / BNB)
    $pair = strtoupper($data->crypto);
    $baseAsset = substr($pair, 0, -3);   // esempio: ENABNB → ENA
    $quoteAsset = substr($pair, -3);     // esempio: ENABNB → BNB

    // ✅ Recupera il prezzo in USD della quote (es. BNB/USDT)
    $quoteAssetPriceUSD = 0;
    $quoteSymbol = $quoteAsset . "USDT"; // es. BNB -> BNBUSDT

    try {
        $binancePriceApi = file_get_contents("https://api.binance.com/api/v3/ticker/price?symbol={$quoteSymbol}");
        if ($binancePriceApi) {
            $priceData = json_decode($binancePriceApi, true);
            $quoteAssetPriceUSD = isset($priceData['price']) ? (float)$priceData['price'] : 0;
        }
    } catch (Exception $e) {
        $quoteAssetPriceUSD = 0;
    }

    // ✅ CALCOLO DELL’ULTIMO POSSESSO (per il residuo)
    $get_lasted_possessed_crypto = "
        SELECT possessed_crypto FROM `history`
        WHERE crypto = '$data->crypto' AND `user` = '{$_SESSION['user_id']}'
        ORDER BY history_id DESC LIMIT 1";
    $runQuery_possessed_crypto = mysqli_query($conn, $get_lasted_possessed_crypto);

    if (!$runQuery_possessed_crypto) {
        echo json_encode([
            "status" => false,
            "message" => "Error SELECT possessed_crypto",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    $result = mysqli_fetch_all($runQuery_possessed_crypto, MYSQLI_ASSOC);
    $previous_possessed = !empty($result) ? (float)$result[0]['possessed_crypto'] : 0;

    // ✅ Get average purchase price for profit calculation
    $avg_price_sql = "
        SELECT
            (SUM(CASE WHEN operation = 'buy' THEN usdt_value ELSE -usdt_value END) /
             SUM(CASE WHEN operation = 'buy' THEN crypto_var ELSE -crypto_var END)) as avg_buy_price
        FROM `history`
        WHERE crypto = '$data->crypto' AND `user` = '{$_SESSION['user_id']}'";

    $avg_price_result = mysqli_query($conn, $avg_price_sql);
    $avg_price_row = mysqli_fetch_assoc($avg_price_result);
    $avg_buy_price = $avg_price_row['avg_buy_price'] ? (float)$avg_price_row['avg_buy_price'] : $sell_price;

    // ✅ IMPLEMENT CORRECT 50/50 PROFIT SPLIT FORMULA
    // Formula from technical report:
    // ETH_keep = qty_buy × (target_price − buy_price) / (2 × target_price)
    // ETH_sell = qty_buy − ETH_keep

    $profit_per_unit = $sell_price - $avg_buy_price;
    $total_profit = $previous_possessed * $profit_per_unit;

    if ($profit_per_unit > 0) {
        // Calculate how much crypto to keep (50% of profit in crypto)
        $crypto_to_keep = $previous_possessed * ($sell_price - $avg_buy_price) / (2 * $sell_price);
        $crypto_to_sell = $previous_possessed - $crypto_to_keep;

        // Ensure we don't sell more than we have
        $crypto_to_sell = min($crypto_to_sell, $previous_possessed);
        $crypto_residual = $previous_possessed - $crypto_to_sell;
    } else {
        // If no profit, sell the amount requested by the bot
        $crypto_to_sell = $qty_sold;
        $crypto_residual = $previous_possessed - $qty_sold;
    }

    // ✅ Valore residuo in USD (opzionale)
    $actual_val = $sell_price * $crypto_residual;

    // ✅ Aggiorna tabella trade
    $s_bid = floatval($data->s_bid);
    $multiplicator = floatval($data->multiplicator);
    $pricelevel = intval($data->pricelevel);

    $suggested_bid = $s_bid * pow($multiplicator, $pricelevel);
    $suggested_bid = number_format($suggested_bid, 10, '.', '');

    $sql = "UPDATE `trade` SET 
                `state_status` = '$data->state_status',
                `suggested_bid` = '$suggested_bid',
                `bid` = 0,
                `crypto_var` = 0,
                `on_actual_price` = 0,
                `ricavo` = 0,
                `future_sell_usdt` = 0,
                `crypto_received` = 0 
            WHERE 
                `pricelevel` = '$data->pricelevel' AND 
                `crypto` = '$data->crypto' AND 
                `user` = '{$_SESSION['user_id']}'";

    $runQuery = mysqli_query($conn, $sql);
    if (!$runQuery) {
        echo json_encode([
            "status" => false,
            "message" => "Errore SQL (update trade SELL)",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    // ✅ Registra in history
    $sql2 = "INSERT INTO `history`(
                `history_date`, `operation`, `usdt_value`, 
                `crypto_var`, `possessed_crypto`, `buy_sell_price`, 
                `actual_value`, `crypto`, `user`
            ) VALUES (
                '$dateTime', '$data->action', '{$usdtValue}', 
                '{$order['origQty']}', '$crypto_residual', 
                '$sell_price', '$actual_val', 
                '$data->crypto', '{$_SESSION['user_id']}'
            )";

    $run_query_sql2 = mysqli_query($conn, $sql2);
    if (!$run_query_sql2) {
        echo json_encode([
            "status" => false,
            "message" => "Errore INSERT history",
            "error" => mysqli_error($conn)
        ]);
        exit;
    }

    // 🔢 Calcolo campi toast usando il prezzo reale USD
    $qty_usd = $qty_sold * $sell_price * $quoteAssetPriceUSD;
    $ricavo_usd = $btc_received * $quoteAssetPriceUSD;
    $crypto_half_usd = $crypto_residual * $sell_price * $quoteAssetPriceUSD;

    // ✅ Get average purchase price for display
    $avg_purchase_price = $avg_buy_price ? number_format($avg_buy_price, 8) : number_format($sell_price, 8);

    // ✅ RISPOSTA COMPLETA PER TOAST E TELEGRAM
    echo json_encode([
        "status" => true,
        "trade_id" => $data->pricelevel,   // ✅ NON più “levellevel50”
        "target_price" => number_format($sell_price, 8),
        "execution_price" => number_format($sell_price, 8), // Actual execution price from Binance
        "avg_purchase_price" => $avg_purchase_price,
        "qty" => number_format($qty_sold, 8),
        "qty_usd" => number_format($qty_usd, 2),
        "ricavo_usd" => number_format($ricavo_usd, 2),
        "usdt_half" => number_format($btc_received, 8),
        "usdt_half_usd" => number_format($btc_received * $quoteAssetPriceUSD, 2),
        "crypto_half" => number_format($crypto_residual, 8),
        "crypto_half_usd" => number_format($crypto_half_usd, 2),
        "base" => $baseAsset,   // ✅ es. ENA
        "quote" => $quoteAsset  // ✅ es. BNB
    ]);

} else {
    echo json_encode([
        "status" => false,
        "massage" => "Problem with api",
        "data" => $order
    ]);
}

mysqli_close($conn);
?>
