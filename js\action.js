// initialize hostname
  const hostname = "http://localhost/bot";

  // initialize the bot status and digit number
  let botStatus = false;

function updateEmporionStatusDisplay() {
  const statusText = document.getElementById("emporion_status_text");
  if (!statusText) return;

  const crypto = document.getElementById("crypto_select")?.value || "";
  const normalized = crypto.replace("/", "");
  const status = localStorage.getItem(`emporion_status_${normalized}`);

  if (status === "open") {
    statusText.textContent = "Emporion is Open";
    statusText.classList.remove("text-danger");
    statusText.classList.add("text-success");
  } else {
    statusText.textContent = "Emporion is Closed";
    statusText.classList.remove("text-success");
    statusText.classList.add("text-danger");
  }
}

updateEmporionStatusDisplay(); // Update status immediately when page loads



let wasOffline = false; // Track if we were offline before

  let digit_number = 8;
let s_bid = parseFloat(localStorage.getItem("s_bid")) || 0.00;

// Prevent repetitive toasts: store last error time by type
const lastToastTime = {
  network: 0,
  credentials: 0,
  binance: 0,
  paramFetch: 0,
  cryptoCheck: 0,
  initError: 0
};

const toastCooldown = 10000; // 10 secondi tra un toast e l’altro

let pollingInterval = null;
let currentCrypto = null;





function startPolling() {
  if (pollingInterval) clearInterval(pollingInterval);

  pollingInterval = setInterval(async () => {
    if (!currentCrypto) return;

    const amount = await getPrice(currentCrypto);
    document.getElementById("actual_price_value").innerHTML = `<p><b>${amount}</b></p>`;

    if (botStatus) await startBot(true);
  }, 10000); // ogni 10 secondi (puoi alzare a 15000-20000 se hai 4 crypto)
}


  // Initialize an empty object to store the targetPrice values
  const targetPriceValues = {};

  async function getCryptoPairInfo(symbol) {
    try {
        const res = await fetch("https://api.binance.com/api/v3/exchangeInfo");
        const data = await res.json();

        const match = data.symbols.find(s => s.symbol === symbol);
        if (!match) {
            console.warn(`Coppia ${symbol} non trovata su Binance`);
            return [symbol, ""];
        }

        return [match.baseAsset, match.quoteAsset]; // [crypto1, crypto2]
    } catch (error) {
  console.error("Errore Binance API:", error);
  const errorMessage = error?.message || "";

  if (errorMessage.includes("Failed to fetch") || errorMessage.includes("NetworkError")) {
   showToastOnce("network", "Binance Network Error", "Unable to connect to Binance. Check your internet connection.");
  logToFile("Binance network error - unable to connect", "network");
  sendToTelegram("disconnected", { crypto: currentCrypto });

  } else if (errorMessage.includes("403") || errorMessage.includes("401") || errorMessage.includes("invalid api") || errorMessage.includes("API-key")) {
   showToastOnce("credentials", "Invalid API Keys", "Binance credentials are incorrect, expired, or unauthorized.");

  } else {
    showToastOnce("binance", "Binance Error", "An error occurred in the request to Binance.");


  }

  return [symbol, ""];
}
}

function updateTableHeaders(crypto1, crypto2) {
  document.getElementById("th-suggested-expense").innerText = `Suggested ${crypto2} Expense`;
  document.getElementById("th-crypto2-expense").innerText = `${crypto2} Expense`;
  document.getElementById("th-crypto1-variation").innerText = `${crypto1} Variation`;
  document.getElementById("th-actual-expense").innerText = `Actual ${crypto2} Expense Value`;
  document.getElementById("th-crypto1-gain").innerText = `${crypto1} Gains (50%)`;
}



// functionality for the start and stop bot

  const budget_input = document.getElementById("budget_input");

  // target also lock and unlock btn
  const lock_btn = document.getElementById("budget_price_lock_btn");
  const unlock_btn = document.getElementById("budget_price_unlock_btn");
  const set_price_btn = document.getElementById("set_price_target");

  const crypto_select = document.getElementById("crypto_select");

  //  price locked by lock button click
  lock_btn.addEventListener("click", () => {
  if (isTableMake()) {
    unlock_btn.classList.remove("disabled");
    lock_btn.classList.add("disabled");
    set_price_btn.classList.add("disabled");

    checkCrypto();
    botStatus = true;
const normalized = crypto_select.value.replace("/", "");
localStorage.setItem(`emporion_status_${normalized}`, "open");
updateEmporionStatusDisplay();

    // ✅ Mostra toast e log
    showToast("success", {
      title: "🟢 Emporion Bot Started",
      message: "The bot is now active and will execute orders."
    });
    logToFile("Emporion Bot started", "info");
    sendToTelegram("start", { crypto: crypto_select.value });

  } else {
    alert("Please select crypto and set the target price!!");
  }
});

  // remove lock by unlock button click
unlock_btn.addEventListener("click", () => {
  unlock_btn.classList.add("disabled");
  lock_btn.classList.remove("disabled");
  set_price_btn.classList.remove("disabled");

  botStatus = false;
const normalized = crypto_select.value.replace("/", "");
localStorage.setItem(`emporion_status_${normalized}`, "closed");
updateEmporionStatusDisplay();

  // ✅ Mostra toast e log
  showToast("warning", {
    title: "🔴 Emporion Bot Stopped",
    message: "The bot has been deactivated. No orders will be executed."
  });
      sendToTelegram("stop", { crypto: crypto_select.value });
  logToFile("Emporion Bot stopped", "info");
});



  //  notifications with sound effects

 const playNotification = () => {
  if (!botStatus) return; // Evita di suonare se il bot è fermo
  let audio = new Audio("./assets/sound.mp3");
  audio.play().catch(e => console.log("Il browser non supporta l’audio o è bloccato."));
};

// Enhanced audio control to prevent loops
const originalPlayNotification = playNotification;
window.playNotification = () => {
  if (!botStatus) return; // Don't play if bot is stopped

  const now = Date.now();
  if (now - (window.lastAudioTime || 0) < 2000) {
    console.log("Audio throttled - too soon since last notification");
    return;
  }

  // Stop any currently playing audio
  if (window.notificationAudio) {
    window.notificationAudio.pause();
    window.notificationAudio.currentTime = 0;
  }

  // Create new audio instance
  let audio = new Audio("./assets/sound.mp3");
  audio.volume = 0.7; // Set reasonable volume
  window.notificationAudio = audio;

  // Play with error handling
  audio.play()
    .then(() => {
      window.lastAudioTime = now;
      console.log("Notification sound played successfully");
    })
    .catch(e => {
      console.log("Browser doesn't support audio or it's blocked:", e.message);
    });

  // Clean up after audio finishes
  audio.addEventListener('ended', () => {
    window.notificationAudio = null;
  });

  // Failsafe: clean up after 5 seconds even if 'ended' event doesn't fire
  setTimeout(() => {
    if (window.notificationAudio) {
      window.notificationAudio.pause();
      window.notificationAudio = null;
    }
  }, 5000);
};

// Override the original function
playNotification = window.playNotification;


  // fetch multiplicator data
 const fetchMultiplicatorDigit = async () => {
    const crypto = crypto_select.value;

    // 🔴 Se non hai selezionato nessuna crypto, esci e non fare nulla
    if (!crypto) {
        console.warn("No crypto selected. Parameters not loaded.");
        return;
    }

    try {
        const res = await fetch("./includes/get_crypto_settings.php", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ crypto })
        });

        const result = await res.json();

        // 🔴 Se mancano i dati importanti, fermati
        if (!result || result.digit_num === undefined) {
            console.error("Missing parameters in response:", result);
            return;
        }

        digit_number = result.digit_num;
        s_bid = parseFloat(result.s_bid);
        localStorage.setItem("s_bid", s_bid);

        document.getElementById("base-expense-display").textContent = s_bid.toFixed(8);
        document.querySelector(".param-multiplicator").textContent = result.multiplicator;
        document.querySelector(".param-digit").textContent = result.digit_num;
               document.querySelector(".param-slippage").textContent = result.slippage_num + "%";

        // Update telegram modal display
        const currentCryptoDisplay = document.getElementById("current-crypto-display");
        if (currentCryptoDisplay) {
            currentCryptoDisplay.textContent = crypto;
        }

        // Load existing telegram credentials if available
        if (result.telegram_token && result.telegram_chat_id) {
            const cryptoKey = crypto.includes("/") ? crypto : crypto.replace(/(BTC|USDT|BNB|ETH|ENJ)$/, "/$1");
            localStorage.setItem(`telegram_token_${cryptoKey}`, result.telegram_token);
            localStorage.setItem(`telegram_chat_id_${cryptoKey}`, result.telegram_chat_id);
        }

        if (wasOffline) handleConnectionRecovery(); // ✅ Mostra toast una sola volta

        return result;

} catch (error) {
  console.error("API Error:", error);
  const errorMessage = error?.message || "";

  if (errorMessage.includes("Failed to fetch") || errorMessage.includes("NetworkError")) {
    showToastOnce("network", "Network Error", "Connection interrupted during parameter retrieval.");


  } else {
showToastOnce("paramFetch", "Parameter Retrieval Error", "Unable to get crypto data from server.");



  }
}

};



  fetchMultiplicatorDigit();

  // make digits for numbers by user choice 
  const digitNumber = (number) => {

    const parts = number.toString().split('.');

    let final_number;
  
    // Check if there is a decimal part
    if (parts.length === 2) {
      final_number = parts[0] + "." + parts[1].slice(0, digit_number);
    } else {
      final_number = parts[0];
    }
  
    return final_number;

  }


  // define target price with specified function

 const targetMany = () => {
  const htmlModal = document.getElementById("target_price_wrapper_input");
htmlModal.innerHTML = `
  <label for="textarea_targets" class="form-label">
    Paste your Target Prices (one per line) <br>
    <span style="color: #a84c3c; font-weight: bold;">(from lowest to highest)</span>:
  </label>
  <textarea id="textarea_targets" class="form-control" rows="10" placeholder="Es:&#10;0,001&#10;0,002&#10;0,003"></textarea>
`;

};


  // getting all target price value and adding them to an object
  const getTargetPrice = () => {
  const textarea = document.getElementById("textarea_targets");
  const lines = textarea.value.split(/\r?\n/);
  const cleaned = lines
    .map(l => l.trim().replace(",", ".")) // converte virgola in punto
    .filter(l => l !== "" && !isNaN(l))
    .map(Number);

  if (cleaned.length === 0) {
    alert("Inserisci almeno un target price valido.");
    return;
  }

  const htmlModal = document.getElementById("target_price_wrapper_input");
  htmlModal.innerHTML = ""; // reset

  cleaned.forEach((price, i) => {
    const levelId = `level${i + 1}`;
    targetPriceValues[levelId] = price;
    htmlModal.innerHTML += `
      <div class="col-md-6 mb-3">
        <label class="form-label">Level ${i + 1}</label>
        <input type="number" id="${levelId}" class="form-control" value="${price}" disabled />
      </div>`;
  });

  document.getElementById("highest_priceTarget_value").innerHTML = `<p><b>${cleaned[cleaned.length - 1]}</b></p>`;
  document.getElementById("lowest_priceTarget_value").innerHTML = `<p><b>${cleaned[0]}</b></p>`;
  document.getElementById("modal_close_btn").click();
};


  // check crypto are available for order by api
  const checkAvailableCrypto = async (symbol)=>{

    try {

      const response = await fetch("./includes/checkCryptoAvailable.php",{
        headers:{"Content-Type": "application/json"},
        method:"POST",
        body:JSON.stringify({symbol})
      })

      const data = await response.json();
  
      if (data.status === false) {
        alert(data.massage);
      }
    
   } catch (error) {
  console.error("Errore controllo crypto:", error);
showToastOnce("cryptoCheck", "Errore verifica crypto", "Impossibile verificare la disponibilità della crypto.");


}



  }


  // check able function the crypto currency are available in database or not
  const checkCrypto = async ()=>{

    const crypto = crypto_select.value;

    try {

      const res = await fetch("./includes/check_crypto.php",{
        headers:{"Content-Type": "application/json"},
        method: "POST",
        body: JSON.stringify({crypto})
      });

      const result = await res.json();

      if (result.status === false) {

        //const s_bid = budget_input.value / Object.keys(targetPriceValues).length;
        for (const key in targetPriceValues) {
          await fetch("./includes/addtrade_cor.php",{
headers: { "Content-Type": "application/json" },
           method:"POST",
           body: JSON.stringify({level:key,target_price:targetPriceValues[key],crypto:crypto,s_bid:s_bid})
            }
          )
        }

        makeTable(crypto);

      }

  } catch (error) {
  console.error("Errore checkCrypto:", error);
showToastOnce("initError", "Errore inizializzazione", "Impossibile preparare la tabella trade per la crypto selezionata.");


}


  }
  
  // check with condition for make table
  const isTableMake = ()=>{

    const cryptocurrency = crypto_select.value;

    const tableBody = document.getElementById("table-body");

    if (tableBody.innerHTML === "") {
          if (Object.keys(targetPriceValues).length === 0 && targetPriceValues.constructor === Object || cryptocurrency === "") {
          
            return false;
      
          }else{
            return true;
          }
    }else{
      return true;
    }

  
  }


  // make table by currant crypto
  const makeTable = async (currency) =>{

    try {

        const res = await fetch("./includes/trade_cor.php",{
          headers:{"Content-Type" : "application/json"},
          method: "POST",
          body: JSON.stringify({currency})
        });
        const data = await res.json();

        if (data.length > 0) {

          let tableHTML="";

        data.forEach(elem =>{

          tableHTML +=`
          <tr>
              <td>${elem.pricelevel}</td>
              <td class="${elem.state_status}">${elem.state_status}</td>
              <td>${elem.multiplicator}</td>
              <td>${elem.suggested_bid}</td>
              <td>${elem.bid}</td>
              <td>${digitNumber(elem.crypto_var)}</td>
<td class="target-price-cell">${digitNumber(elem.target_price)}</td>
              <td>${digitNumber(elem.on_actual_price)}%</td>
              <td>${digitNumber(elem.ricavo)}</td>
              <td>${digitNumber(elem.future_sell_usdt)}</td>
              <td>${digitNumber(elem.crypto_received)}</td>
            </tr>
          `
        });


        document.getElementById("table-body").innerHTML=tableHTML;
        
        // update the maximum Resistance Price
        const higherPrice = document.getElementById("highest_priceTarget_value");
        higherPrice.innerHTML = `<p><b>${digitNumber(data[0].target_price)}</b></p>`;

        // update the Maximum Support Price
        const lowPrice = document.getElementById("lowest_priceTarget_value");
        lowPrice.innerHTML = `<p><b>${digitNumber(data[data.length - 1].target_price)}</b></p>`;

        }else{
          document.getElementById("table-body").innerHTML="";
        
        // update the maximum Resistance Price
        const higherPrice = document.getElementById("highest_priceTarget_value");
        higherPrice.innerHTML = `<p><b>0</b></p>`;

        // update the Maximum Support Price
        const lowPrice = document.getElementById("lowest_priceTarget_value");
        lowPrice.innerHTML = `<p><b>0</b></p>`;

        }

    } catch (error) {
      console.log(error);
    }

  }


  // new crypto currency fetch function
  const crypto_fetch = async()=>{
    try {

      const res = await fetch("https://api.binance.com/api/v3/ticker/price");
      const data = await res.json();
      const select_option = document.getElementById("new_crypto_select");

      data.forEach(elem =>{
        const option = document.createElement('option');
        option.value = elem.symbol;
        option.textContent = elem.symbol;
        select_option.appendChild(option);
      })

    } catch (error) {
  console.error("Errore caricamento criptovalute:", error);
showToastOnce("binance", "Errore Binance", "Impossibile recuperare l’elenco delle crypto da Binance.");

}

  }

  crypto_fetch();


  // add new crypto options into data by form submit 
  const crypto_form = document.getElementById("add_new_crypto_form");

  crypto_form.addEventListener("submit",async (e) => {
    e.preventDefault();

    const value = document.getElementById("new_crypto_select").value;
    
    if (value !== "") {
      try {

        const data_crypto = JSON.stringify({value});

        const res = await fetch("./includes/crypto_cor.php",{
          headers:{"Content-Type": "application/json"},
          method: "POST",
          body:data_crypto
        });
        const result = await res.json();
        
        if(result.status){
          alert("You have added a new crypto currency");
            window.location = hostname;
        }else{
          alert("Something went wrong. Please try again!!")
        }
        
} catch (error) {
  console.error("Errore aggiunta crypto:", error);
showToastOnce("initError", "Errore salvataggio", "Non è stato possibile aggiungere la nuova crypto al database.");


}

    } else {
      alert("Please select a new crypto currency");
    }
    
  })


    // get actual price 
    const getPrice = async (symbol) => {
      try {
  
        const res = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`);
  
        const data = await res.json();
        
        const amount = digitNumber(data.price);
        
        return amount;

      } catch (error) {
        console.log(error);
      }
    }

    
    // check which level is full and update those level gain and actual price
      const updateCryptoRange = async (currency,actualPrice)=>{

        try {
          
            //  fetch all data from database
            const res = await fetch("./includes/trade_cor.php",{
              headers:{"Content-Type" : "application/json"},
              method: "POST",
              body: JSON.stringify({currency})
            });
            const data = await res.json();
            
            if (data.length > 0) {
              // if have then loop for check state and set bid function
            for (let i = 0; i < data.length; i++) {
                
              if (data[i].state_status === "full" ) {

                const bid  = data[i].bid;
                // set on actual price existing excel formula
                
                const onActualPrice = (100 * actualPrice / data[i].target_price / 100) - 1;
                const newOnActualPrice = onActualPrice * 100;

                // set ricavo existing excel formula
                const newGains = (bid * newOnActualPrice) / 100;
                
                const gain = newGains / 2;

                // set gains ratio 50/50 usdt
                const newFutureSellUsdt = Number(bid) + gain;
                
                // set crypto receive existing excel formula
                const newCryptoReceived = (newGains /2) / data[i].target_price;
                
                // store all data in an object for make json format
                const updateData ={
                  pricelevel : data[i].pricelevel,
                  on_actual_price: newOnActualPrice,
                  ricavo: newGains,
                  future_sell_usdt: newFutureSellUsdt,
                  crypto_received: newCryptoReceived,
                  crypto: currency,
                  actualPrice: actualPrice,
                }

                const response = await fetch("./includes/update_trade_cor.php",{
                  headers:{"Content-Type": "application/json"},
                  method:"POST",
                  body: JSON.stringify(updateData)
                })
                
                const result = await response.json();

                makeTable(currency);

              }

            }

          }

        } catch (error) {
          console.log(error);
        }


      }

    // check target price state and set bid function
   
const startBot = async (status) => {
  if (status) {
    const multi = await fetchMultiplicatorDigit();
    const currency = crypto_select.value;
    const [baseAsset, quoteAsset] = await getCryptoPairInfo(currency);

    // Recupera prezzo attuale
    let actualPrice = document.getElementById("actual_price_value").innerText.replace(/[^0-9.,]/g, "");
    actualPrice = parseFloat(actualPrice.replace(",", "."));
    if (isNaN(actualPrice)) {
      actualPrice = await getPrice(currency); // solo se non esiste un valore valido
    }

    // calcola slippage
    const calPercent = (actualPrice * multi.slippage_num) / 100;
    const lowRange = Number(actualPrice) - calPercent;
    const highRange = Number(actualPrice) + calPercent;

    try {
      // recupera i dati dal database
      const res = await fetch("./includes/trade_cor.php", {
        headers: { "Content-Type": "application/json" },
        method: "POST",
        body: JSON.stringify({ currency })
      });
      const data = await res.json();

      if (data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          // check se il prezzo attuale colpisce un target
          if (digitNumber(highRange) >= digitNumber(data[i].target_price) &&
              digitNumber(lowRange) <= digitNumber(data[i].target_price)) {

            // blocco BUY
            if (data[i].state_status === "open") {
              let multiple = Number(data[i].multiplicator);
              const newMultiple = multiple + 1;
              const newBid = s_bid * Math.pow(multi.multiplicator, data[i].multiplicator);
              const newState = "full";

              const updateData = {
                pricelevel: data[i].pricelevel,
                state_status: newState,
                multiplicator: newMultiple,
                s_bid: newBid,
                bid: newBid,
                crypto: currency,
                actualPrice: actualPrice,
                action: "buy"
              };

              const response = await fetch("./includes/updatetradebuy_cor.php", {
                headers: { "Content-Type": "application/json" },
                method: "POST",
                body: JSON.stringify(updateData)
              });

              const result = await response.json();
              console.log("✅ BUY result:", result);

              showToast("buy", result);
              sendToTelegram("buy", result);
              playNotification();
              makeTable(currency);
              logToFile(
                `BUY Order – TradeID: ${result.trade_id}, Expense: ${result.spesa} ${result.quote}, Quantity: ${result.qty} ${result.base}, Level: ${result.level_from}→${result.level_to}`,
                "buy"
              );
            }
            // blocco SELL
            else if (data[i].state_status === "full") {
              if (data[i + 1].state_status === "full") {
                const bid = data[i + 1].bid;
                const preBid = data[i + 1].suggested_bid;
                const newState = "open";

                const updateData = {
                  pricelevel: data[i + 1].pricelevel,
                  state_status: newState,
                  s_bid: preBid,
                  multiplicator: data[i + 1].multiplicator,
                  future_sell_usdt: Number(bid),
                  crypto: currency,
                  actualPrice: actualPrice,
                  action: "sell"
                };

                const response = await fetch("./includes/updatetradesell_cor.php", {
                  headers: { "Content-Type": "application/json" },
                  method: "POST",
                  body: JSON.stringify(updateData)
                });

                const result = await response.json();
                console.log("✅ SELL result:", result);

                showToast("sell", result);
                sendToTelegram("sell", result);
                playNotification();
                makeTable(currency);
                await fetchMultiplicatorDigit();
                logToFile(
                  `SELL Order – TradeID: ${result.trade_id}, Quantity sold: ${result.qty} ${result.base}, Net profit: +${result.ricavo_usd} USD`,
                  "sell"
                );
              } // fine if SELL interno
            } // fine else if stato full

          } // fine if prezzo in range
        } // fine for

        // ✅ solo dopo il for
        updateCryptoRange(currency, actualPrice);

      } // fine if data.length > 0
    } catch (error) {
      console.log(error);
    }

  } // fine if status
};


    const reset_price = async (symbol)=>{
      
      const response = await fetch("./includes/delete_target_price.php",{
        headers:{"Content-Type": "application/json"},
        method: "POST",
        body: JSON.stringify({symbol})
      })
      const data = await response.json();
      if (data.status) {
        makeTable(symbol);
      } else {
        alert("Something went wrong!!");
      }

    }
    const delete_reset_price = () =>{
      const symbol = crypto_select.value

      if (confirm('Are you sure to reset the target price?')) {
        reset_price(symbol);
      }

    };


    


// Quando si seleziona una crypto dal menu a tendina, aggiorna tutti i form
crypto_select.addEventListener("change", (e) => {
    document.querySelectorAll("input[name='crypto']").forEach(input => {
        input.value = e.target.value;
    });
});


document.getElementById("crypto_filter").addEventListener("input", function () {
    const filter = this.value.toUpperCase();
    const options = document.querySelectorAll("#new_crypto_select option");

    options.forEach(option => {
        const text = option.textContent.toUpperCase();
        if (text.includes(filter) || option.value.includes(filter)) {
            option.style.display = "";
        } else {
            option.style.display = "none";
        }
    });
});

// Gestione invio form del modal slippage
document.querySelector('#slippage_modal form').addEventListener('submit', async function (e) {
    e.preventDefault();

    const form = e.target;
    const crypto = document.getElementById("crypto_select").value;
    const slippage_val = form.querySelector('input[name="slippage_num_val"]').value;

    try {
        const res = await fetch("includes/slippage_cor.php", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                slippage_num_val: slippage_val,
                crypto: crypto
            })
        });

        const result = await res.json();

        if (result.status) {
            // Ricarica i valori aggiornati visualmente
            await fetchMultiplicatorDigit();

            // Chiudi il modal
            const modal = bootstrap.Modal.getInstance(document.getElementById("slippage_modal"));
            modal.hide();
        } else {
            alert("Error saving: " + result.message);
        }
    } catch (error) {
        console.error("Error saving slippage:", error);
    }
});

// Gestione invio form del modal base expense
document.querySelector('#base-expense-form').addEventListener('submit', async function (e) {
    e.preventDefault();

    const form = e.target;
    const crypto = document.getElementById("crypto_select").value;
    const base_expense_val = form.querySelector('input[name="base_expense_val"]').value;

    if (!crypto) {
        alert("Please select a crypto pair first.");
        return;
    }

    if (!base_expense_val || isNaN(base_expense_val) || parseFloat(base_expense_val) <= 0) {
        alert("Please enter a valid base expense value.");
        return;
    }

    try {
        const res = await fetch("includes/sbid_cor.php", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                s_bid: parseFloat(base_expense_val),
                crypto: crypto
            })
        });

        const result = await res.json();

        if (result.status) {
            // Update the display value
            document.getElementById("base-expense-display").textContent = parseFloat(base_expense_val).toFixed(8);

            // Update the global s_bid variable
            s_bid = parseFloat(base_expense_val);
            localStorage.setItem("s_bid", s_bid);

            // Reload crypto settings to ensure consistency
            await fetchMultiplicatorDigit();

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById("baseExpenseModal"));
            modal.hide();

            // Clear the form
            form.reset();

            showToast("success", {
                title: "✅ Base Expense Updated",
                message: `Base expense set to ${parseFloat(base_expense_val).toFixed(8)} for ${crypto}`
            });
        } else {
            alert("Error saving base expense: " + (result.message || "Unknown error"));
        }
    } catch (error) {
        console.error("Error saving base expense:", error);
        alert("Network error while saving base expense.");
    }
});

// Gestione invio form del modal telegram configuration
document.querySelector('#telegram-config-form').addEventListener('submit', async function (e) {
    e.preventDefault();

    const form = e.target;
    const crypto = document.getElementById("crypto_select").value;
    const telegram_token = form.querySelector('input[name="telegram_token"]').value;
    const telegram_chat_id = form.querySelector('input[name="telegram_chat_id"]').value;

    if (!crypto) {
        alert("Please select a crypto pair first.");
        return;
    }

    if (!telegram_token || !telegram_chat_id) {
        alert("Please enter both token and chat ID.");
        return;
    }

    try {
        const res = await fetch("includes/telegram_cor.php", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                telegram_token: telegram_token,
                telegram_chat_id: telegram_chat_id,
                crypto: crypto
            })
        });

        const result = await res.json();

        if (result.status) {
            // Update localStorage for immediate use
            const cryptoKey = crypto.includes("/") ? crypto : crypto.replace(/(BTC|USDT|BNB|ETH|ENJ)$/, "/$1");
            localStorage.setItem(`telegram_token_${cryptoKey}`, telegram_token);
            localStorage.setItem(`telegram_chat_id_${cryptoKey}`, telegram_chat_id);

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById("telegramConfigModal"));
            modal.hide();

            // Clear the form
            form.reset();

            showToast("success", {
                title: "✅ Telegram Configuration Saved",
                message: `Telegram bot configured for ${crypto}`
            });
        } else {
            alert("Error saving telegram configuration: " + (result.message || "Unknown error"));
        }
    } catch (error) {
        console.error("Error saving telegram configuration:", error);
        alert("Network error while saving telegram configuration.");
    }
});



  // change actual price by selected currency

 crypto_select.addEventListener("change", async (e) => {
  const symbol = e.target.value;
    currentCrypto = symbol;
  startPolling(); // avvia polling centralizzato
updateEmporionStatusDisplay(); // 🟢 aggiorna etichetta per nuova crypto

  const [crypto1, crypto2] = await getCryptoPairInfo(symbol);
updateTableHeaders(crypto1, crypto2);


  // 🆕 Carica i valori della crypto selezionata
  await fetchMultiplicatorDigit();

  const amount = await getPrice(symbol);
  document.getElementById("actual_price_value").innerHTML = `<p><b>${amount}</b></p>`;

  checkAvailableCrypto(symbol);

  document.getElementById("reset_btn_wrapper").innerHTML = `<button class="btn btn-danger" id="reset_btn" onclick="delete_reset_price()">Reset All Target Price for ${symbol}</button>`;

  makeTable(symbol);
  startBot(botStatus);



});
const getUsdValue = async (amount, assetSymbol) => {
  try {
    const res = await fetch("includes/get_usd_value.php", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ amount, asset: assetSymbol })
    });

    const result = await res.json();

    if (result.status) {
      return result.usd_value;
    } else {
      console.warn("USD Value Error:", result.message);
      return 0;
    }
  } catch (error) {
    console.error("USD conversion error:", error);
    return 0;
  }
};


function showToastOnce(typeKey, title, message) {
  const now = Date.now();
  if (now - (lastToastTime[typeKey] || 0) > toastCooldown) {
    lastToastTime[typeKey] = now;
    showToast("error", { title, message });
  }
}



function handleConnectionRecovery() {
  if (wasOffline) {
    wasOffline = false;
    showToast("success", {
      title: "✅ Connection Restored",
      message: "Connection with Binance has been restored. The bot is active again."
    });
    sendToTelegram("reconnected", { crypto: currentCrypto });
  }
}


function logToFile(message, type = "info") {
  fetch("./includes/log_message.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      message: message,
      type: type
    })
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  })
  .then(data => {
    if (!data.status) {
      console.warn("Log file error:", data.message);
    } else {
      console.log("Log entry added:", message);
    }
  })
  .catch(e => {
    console.warn("Log file error:", e.message);
  });
}


async function sendToTelegram(type, data = {}) {
const cryptoKeyRaw = data.crypto || currentCrypto || "UNKNOWN";
const cryptoKey = cryptoKeyRaw.includes("/") ? cryptoKeyRaw : cryptoKeyRaw.replace(/(BTC|USDT|BNB|ETH|ENJ)$/, "/$1");

  // Try to get from localStorage first, then from database
  let token = localStorage.getItem(`telegram_token_${cryptoKey}`);
  let chat_id = localStorage.getItem(`telegram_chat_id_${cryptoKey}`);

  // If not in localStorage, try to get from database
  if (!token || !chat_id) {
    try {
      const res = await fetch("includes/get_telegram_credentials.php", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ crypto: cryptoKeyRaw })
      });
      const result = await res.json();

      if (result.status && result.telegram_token && result.telegram_chat_id) {
        token = result.telegram_token;
        chat_id = result.telegram_chat_id;

        // Cache in localStorage for future use
        localStorage.setItem(`telegram_token_${cryptoKey}`, token);
        localStorage.setItem(`telegram_chat_id_${cryptoKey}`, chat_id);
      }
    } catch (error) {
      console.error("Error fetching telegram credentials:", error);
    }
  }

  if (!token || !chat_id) {
    console.warn("Telegram bot not configured for", cryptoKey);
    return;
  }

 let message = "";
switch (type) {
  case "start":
    message = `🟢 Emporion Bot avviato\nIl bot è ora attivo ed eseguirà ordini per ${data.crypto}.`;
    break;
  case "stop":
    message = `🔴 Emporion Bot fermato\nIl bot è stato arrestato per ${data.crypto}.`;
    break;
  case "disconnected":
    message = `⚠️ Emporion ha perso la connessione a Binance\nVerifica la rete o ricarica la pagina.`;
    break;
  case "reconnected":
    message = `🔌 Connessione a Binance ripristinata\nEmporion sta riprendendo le operazioni per ${data.crypto}.`;
    break;
  case "buy":
    message = `📈 Buy Order Executed
• Trade ID: ${data.trade_id}
• Target price: ${data.target_price}
• Execution price: ${data.execution_price || data.target_price}
• Average purchase price: ${data.avg_purchase_price || 'N/A'}
• Quantity bought: ${data.qty} ${data.base} (~${data.qty_usd} USD)
• Expense: ${data.spesa} ${data.quote} (~${data.spesa_usd} USD)
• Level: ${data.level_from} → ${data.level_to}`;
    break;
  case "sell":
    message = `📉 Sell Order Executed
• Trade ID: ${data.trade_id}
• Target price: ${data.target_price}
• Execution price: ${data.execution_price || data.target_price}
• Average purchase price: ${data.avg_purchase_price || 'N/A'}
• Quantity sold: ${data.qty} ${data.base} (~${data.qty_usd} USD)
• Net profit: +${data.ricavo_usd} USD
• 50/50 Split: ${data.usdt_half} ${data.quote} (~${data.usdt_half_usd} USD) + ${data.crypto_half} ${data.base} (~${data.crypto_half_usd} USD)`;
    break;
  default:
    message = `📢 Emporion Notification`;
}


  const url = `https://api.telegram.org/bot${token}/sendMessage?chat_id=${chat_id}&text=${encodeURIComponent(message)}`;
  fetch(url)
    .then(res => res.json())
    .then(res => console.log("📨 Telegram sent:", type, "\nContent:", message))
    .catch(err => console.error("❌ Telegram error:", err));
}




function removeSelectedCrypto() {
  const selected = document.getElementById("remove_crypto_select").value;
  if (!selected) return alert("Select a pair to remove.");

  if (!confirm(`Are you sure you want to remove ${selected}?`)) return;

  fetch("./includes/remove_crypto.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ value: selected })
  })
  .then(res => res.text())
.then(text => {
  try {
    const data = JSON.parse(text);
    if (data.status) {
      alert("Crypto removed successfully.");
      location.reload();
    } else {
      alert("Failed to remove crypto: " + data.message);
    }
  } catch (e) {
    console.error("Remove error: invalid response", text); // ← Show the real HTML error
    alert("Internal error: invalid response from server.");
  }
})
.catch(err => {
  console.error("Remove error (fetch failed):", err);
  alert("Network error removing crypto.");
});

}

document.getElementById("export_excel_btn").addEventListener("click", async () => {
  const crypto = document.getElementById("crypto_select").value;
  if (!crypto) return alert("Seleziona una coppia crypto.");

  try {
    const res = await fetch("./includes/export_excel.php", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: `crypto=${encodeURIComponent(crypto)}`
    });

    const filename = await res.text();

    // Forza il download
    const link = document.createElement("a");
    link.href = "./Excel/" + filename;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (err) {
    console.error("Errore export Excel:", err);
    alert("Impossibile esportare la tabella.");
  }
});
