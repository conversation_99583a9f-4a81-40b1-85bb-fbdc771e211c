<?php
session_start();
include "conn.php";

$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data["crypto"];
$telegram_token = $data["telegram_token"];
$telegram_chat_id = $data["telegram_chat_id"];

if (!$crypto) {
    echo json_encode(["status" => false, "message" => "Crypto pair is required"]);
    exit;
}

if (!$telegram_token || !$telegram_chat_id) {
    echo json_encode(["status" => false, "message" => "Both token and chat ID are required"]);
    exit;
}

// Update the crypto table with telegram credentials
$sql = "UPDATE crypto SET telegram_token = ?, telegram_chat_id = ? WHERE crypto_name = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("sssi", $telegram_token, $telegram_chat_id, $crypto, $_SESSION['user_id']);

if ($stmt->execute()) {
    echo json_encode(["status" => true, "message" => "Telegram credentials saved successfully"]);
} else {
    echo json_encode(["status" => false, "message" => "Error saving telegram credentials: " . $conn->error]);
}

$stmt->close();
$conn->close();
?>
