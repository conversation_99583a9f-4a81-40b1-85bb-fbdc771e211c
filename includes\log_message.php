<?php
// FILE: includes/log_message.php

// Set content type
header('Content-Type: application/json');

// Read JSON body
$data = json_decode(file_get_contents("php://input"), true);

// Validate input data
if (!$data || !isset($data["message"])) {
    http_response_code(400);
    echo json_encode(["status" => false, "message" => "Missing data"]);
    exit;
}

$message = trim($data["message"]);
$type = strtoupper($data["type"] ?? "INFO");

// Validate message is not empty
if (empty($message)) {
    http_response_code(400);
    echo json_encode(["status" => false, "message" => "Empty message"]);
    exit;
}

// Format log line with timestamp and type
$logLine = "[" . date("Y-m-d H:i:s") . "][$type] $message" . PHP_EOL;

// Determine log file path
$logPath = dirname(__DIR__) . "/log/bot.log";

// Ensure log directory exists
$logDir = dirname($logPath);
if (!is_dir($logDir)) {
    if (!mkdir($logDir, 0755, true)) {
        http_response_code(500);
        echo json_encode(["status" => false, "message" => "Failed to create log directory"]);
        exit;
    }
}

// Write to log file with error handling
$result = file_put_contents($logPath, $logLine, FILE_APPEND | LOCK_EX);

if ($result === false) {
    http_response_code(500);
    echo json_encode(["status" => false, "message" => "Failed to write to log file"]);
    exit;
}

// Respond with success
echo json_encode(["status" => true, "message" => "Log entry added successfully"]);
?>
