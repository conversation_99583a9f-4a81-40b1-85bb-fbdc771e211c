<?php

 include "includes/header.php";
 include "includes/functions.php";

if (isset($_GET['start_bot'])) {

    // ✅ Leggi subito la crypto selezionata
    $selected_crypto = $_SESSION['selected_crypto'] ?? null;

    // 🔁 <PERSON>udi tutte le crypto
    $_SESSION['emporion_status'] = [];

    // ✅ Apri solo quella selezionata
    if ($selected_crypto) {
        $_SESSION['emporion_status'][$selected_crypto] = 'open';

        // ✅ Telegram message will be sent via JavaScript
    }
}



?>
<!-- STILE MITOLOGICO -->
<style>
    @import url('https://fonts.googleapis.com/css2?family=Cinzel&display=swap');

    body {
        font-family: 'Cinzel', serif;
        background-color: #fdf6e3;
        color: #2d1f1f;
    }

    .cornucopia-section {
        background-image: linear-gradient(rgba(253, 246, 227, 0.9), rgba(253, 246, 227, 0.9)), url('img/cornucopia.jpg');
        background-size: cover;
        background-position: center;
        padding: 40px 20px;
        border-bottom: 2px solid #c49e57;
        color: #2d1f1f;
    }


a.btn {
    color: #fff !important;
}

a.btn:hover {
    color: #fefae0 !important;
    text-decoration: none;
}


    .info-box {
        background-color: rgba(255, 250, 230, 0.85);
        border: 2px solid #c2a76b;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 1px 1px 4px rgba(0,0,0,0.1);
    }

    select, input, button {
        background-color: #f5f0e1;
        border: 1px solid #c2a76b;
        border-radius: 8px;
        padding: 6px 10px;
        font-size: 14px;
    }

    table {
        background-color: #fffdf6;
        border: 1px solid #c49e57;
    }

    th, td {
        border: 1px solid #c49e57;
        padding: 8px;
    }

    ul {
        list-style: none;
        padding: 0;
    }

    ul li a {
        color: #3b2f2f;
        text-decoration: none;
    }

    ul li a:hover {
        text-decoration: underline;
    }

    /* Pulsanti personalizzati */
    .btn-warning {
        background-color: #d1a754 !important;
        border-color: #b78e3b;
        color: #fff;
    }

    .btn-primary {
        background-color: #6987a0 !important;
        border-color: #4f6c81;
        color: #fff;
    }

    .btn-success {
        background-color: #88a76d !important;
        border-color: #6d8b54;
        color: #fff;
    }







    .btn-danger {
        background-color: #a84c3c !important;
        border-color: #8e3f31;
        color: #fff;
    }

    .btn-secondary {
        background-color: #b8b2a6 !important;
        border-color: #9d978a;
        color: #fff;
    }



    .btn:disabled {
        opacity: 0.6;
    }

    .btn {
        font-family: 'Cinzel', serif;
        box-shadow: 1px 1px 3px rgba(0,0,0,0.1);
    }

    td.target-price-cell {
    background-color: #d8e4c3 !important; /* verde oliva chiaro e sobrio */
    color: #2d1f1f;
    font-weight: bold;
}

/* 1. Nasconde visivamente i link, ma li lascia attivi */
.visually-hidden {
    display: inline-block;
    width: 0;
    height: 0;
    overflow: hidden;
    font-size: 0;
    color: transparent;
    pointer-events: auto;
}

/* 2. Stile a bottone antico per link testuali */
.styled-button {
    display: inline-block;
    padding: 6px 12px;
    margin: 5px 0;
    background-color: #f0e5cf;
    border: 1px solid #c2a76b;
    border-radius: 8px;
    font-weight: 600;
    font-size: 15px;
    color: #3b2f2f;
    text-align: center;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.styled-button:hover {
    background-color: #e6d4ad;
    color: #2d1f1f;
    text-decoration: none;
}

/* 3. Disposizione verticale dei bottoni */
.column-buttons {
    display: flex;
    flex-direction: column;
}

.menu-column {
    padding-left: 0;
    list-style: none;
    margin-bottom: 10px;
}

.param-value {
    font-family: 'Cinzel', serif;
    background-color: rgba(255, 248, 230, 0.6);
    border: 1px solid #c2a76b;
    padding: 6px 12px;
    border-radius: 8px;
    color: #4b3621;
    font-size: 14px;
    width: 130px;
    text-align: center;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 1px 1px 2px rgba(0,0,0,0.05);
}

.visually-hidden {
  display: none;
}

.excel-button {
    background-color: #7ab77a; /* verde Excel tenue */
    color: #fff;
    font-family: 'Cinzel', serif;
    font-size: 14px;
    font-weight: normal;
    border: 1px solid #5d9a5d;
    border-radius: 8px;
    padding: 6px 12px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.excel-button:hover {
    background-color: #6a9f6a; /* leggermente più scuro */
    box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.2);
}

.excel-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.side-button {
    display: block;
    width: 100%;
    min-width: 200px;
    margin: 6px 0; /* margine uniforme sopra e sotto */
    text-align: center;
    padding: 8px 16px; /* uguale per tutti */
    font-size: 14px;
}

/* Nuovi stili pulsanti laterali coerenti */

.btn-telegram {
  background-color: #6987a0;
  border-color: #4f6c81;
  color: #fff;
}
.btn-telegram:hover {
  background-color: #5e728d;
}

.btn-crypto {
  background-color: #9e799d;
  border-color: #7c5f7d;
  color: #fff;
}
.btn-crypto:hover {
  background-color: #7c5f7d;
}

.btn-export {
  background-color: #7a9f6a;
  border-color: #6d8b54;
  color: #fff;
}
.btn-export:hover {
  background-color: #6d8b54;
}

.btn-save {
  background-color: #a37457;
  border-color: #8c6049;
  color: #fff;
}
.btn-save:hover {
  background-color: #8c6049;
}

.btn-load {
  background-color: #bfa37c;
  border-color: #a68b6a;
  color: #fff;
}
.btn-load:hover {
  background-color: #a68b6a;
}

.btn-user {
  background-color: #8d7c6b;
  border-color: #736457;
  color: #fff;
}
.btn-user:hover {
  background-color: #736457;
}

.btn-logout {
  background-color: #c14f3d;
  border-color: #9e3d2c;
  color: #fff;
}
.btn-logout:hover {
  background-color: #9e3d2c;
}


.telegram-config-button {
    background-color: #6987a0;  /* stesso colore attuale di Add New Crypto Pair */
    color: #fff;
    font-family: 'Cinzel', serif;
    font-size: 14px;
    font-weight: normal;
    border: 1px solid #4f6c81;
    border-radius: 8px;
    padding: 6px 12px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

.telegram-config-button:hover {
    background-color: #5a6e84;
}





.sql-load-button {
    background-color: #c1abc9;  /* viola chiaro più scuro */
    color: #fff;  /* uniforme con gli altri pulsanti */
    font-family: 'Cinzel', serif;
    font-size: 14px;
    font-weight: normal;
    border: 1px solid #a68bb0;
    border-radius: 8px;
    padding: 6px 12px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

.sql-load-button:hover {
    background-color: #ae9ab8;
    color: #fefae0;
}



.notification {
    background-color: #f44336;
    color: white;
    padding: 12px 18px;
    margin-bottom: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    font-family: 'Cinzel', serif;
    animation: fadeInOut 4s forwards;
}

.notification.success {
    background-color: #4CAF50;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-20px); }
    10% { opacity: 1; transform: translateY(0); }
    90% { opacity: 1; }
    100% { opacity: 0; transform: translateY(-20px); }
}

.emporion-status {
    font-family: 'Cinzel', serif;
    font-size: 16px;
    margin-top: 10px;
    text-align: center;
}

.emporion-open {
    color: #3e7c3e; /* verde bosco */
    font-weight: bold;
}

.emporion-closed {
    color: #a74a4a; /* rosso scuro */
    font-weight: bold;
}

.emporion-status {
    font-family: 'Cinzel', serif;
    font-size: 16px;
    margin-top: 10px;
    text-align: center;
}

.emporion-open {
    color: #3e7c3e; /* verde bosco */
    font-weight: bold;
}

.emporion-closed {
    color: #a74a4a; /* rosso scuro */
    font-weight: bold;
}

/* Hover uniforme per START BOT */
.btn-success:hover {
    background-color: #6d8b54 !important;
    color: #f0e5cf !important; /* testo leggermente più scuro */
    text-shadow: none;
}

/* Hover uniforme per STOP BOT */
.btn-secondary:hover {
    background-color: #9d978a !important;
    color: #f0e5cf !important;
    text-shadow: none;
}

/* Hover uniforme per SAVE SQL */
.btn-save:hover {
    background-color: #8c6049 !important;
    color: #f0e5cf !important;
    text-shadow: none;
}

.btn-export-excel {
  background-color: #5aa773; /* Verde elegante */
  color: white;
  border-radius: 8px;
  font-family: 'Cinzel', serif;
  font-weight: bold;
  font-size: 15px;
  padding: 8px 16px;
  border: none;
  transition: background-color 0.2s, color 0.2s;
}

.btn-export-excel:hover {
  background-color: #3f8559;
  color: #f8f8f8;
}


.sql-button {
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  width: 100%;
  margin-top: 10px;
  font-family: 'Cinzel', serif;
  font-size: 15px;
  transition: all 0.3s ease;
}

.sql-button:hover {
  filter: brightness(1.05);
  color: #2b1d12;
  cursor: pointer;
}

/* Specifici colori */
#save-sql {
  background-color: #9c6b4a;
  color: white;
}

#load-sql {
  background-color: #c4aa84;
  color: #fff;
}

.button-emp {
  font-family: 'Cinzel', serif;
  font-size: 14px;
  text-transform: uppercase;
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  width: 100%;
  margin-top: 10px;
  transition: all 0.3s ease;
  color: white;
}

.button-emp:hover {
  filter: brightness(1.08);
  color: #2b1d12;
  cursor: pointer;
}

/* Colori specifici */
.button-excel {
  background-color: #628a5e;
}

.button-save-sql {
  background-color: #9c6b4a;
}

.button-load-sql {
  background-color: #c4aa84;
  color: #f9f4e8; /* per contrasto */
}


.summary-box {
  font-family: 'Cinzel', serif;
  font-size: 14px;
  background-color: #f4ebdc;
  color: #5e3b1b;
  border: 2px solid #c2a176;
  border-radius: 10px;
  padding: 8px 16px;
  margin-top: 10px;
  width: fit-content;
  text-align: center;
  box-shadow: 1px 1px 4px rgba(0,0,0,0.1);
}

.summary-box {
  font-family: 'Cinzel', serif;
  font-size: 15px;
  background-color: #fdf6e3;
  color: #5e3b1b;
  border: 2px solid #c2a176;
  border-radius: 10px;
  padding: 10px 25px;
  margin-top: 10px;
  text-align: center;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 1px 1px 4px rgba(0,0,0,0.1);
}

.summary-line {
  margin: 2px 0;
  white-space: nowrap;
}


</style>

<header class="cornucopia-section">
    <div class="container">
<div class="row d-flex align-items-start">
  <!-- SINISTRA -->
  <div class="col-md-4">
    <div class="crypto_actualPrice_wrapper">
      <!-- Resistance -->
      <div class="info-box d-flex align-items-center justify-content-between">
        <div><p>Resistance Value</p></div>
        <div id="highest_priceTarget_value"><p><b>0</b></p></div>
      </div>

      <!-- Support -->
      <div class="info-box d-flex align-items-center justify-content-between">
        <div><p>Support Value</p></div>
        <div id="lowest_priceTarget_value"><p><b>0</b></p></div>
      </div>

      <!-- Crypto select -->
      <div class="info-box d-flex align-items-center justify-content-between">
        <div><p>Crypto Pair</p></div>
        <div class="crypto_select_wrapper">
          <select id="crypto_select" class="form-select">
            <option value="" disabled selected>Select Crypto Pair</option>
            <?php 
              $sql = "SELECT * FROM crypto WHERE `user` = '{$_SESSION['user_id']}'";
              $runQuery = mysqli_query($conn, $sql) or die("runQuery failed");
              while ($row = mysqli_fetch_array($runQuery)){
                  echo "<option value='{$row['crypto_name']}'>{$row['crypto_name']}</option>";
              }
            ?>
          </select>
          <?php 
            $get_budget_sql = "SELECT budget FROM personal_data WHERE pid = '{$_SESSION['user_id']}'";
            $runQuery_get_budget_sql = mysqli_query($conn, $get_budget_sql) or die("Couldn't get budget");
            $budget = mysqli_fetch_assoc($runQuery_get_budget_sql);
          ?>
          <input type="hidden" id="budget_input" value="<?php echo $budget['budget'] ?>">
        </div>
      </div>

      <!-- Actual Price -->
      <div class="info-box d-flex align-items-center justify-content-between border-0">
        <div><p>Actual Price</p></div>
        <div id="actual_price_value"><p><b>-</b></p></div>
      </div>

   

      <!-- Bottoni Start/Stop -->
<button class="btn btn-success" id="budget_price_lock_btn" onclick="startBot()">Start Bot</button>
<button class="btn btn-secondary disabled" id="budget_price_unlock_btn" onclick="setEmporionStatus(false)">Stop Bot</button>

    </div>
  </div>

  <!-- CENTRO -->
  <div class="col-md-4">
    <div class="page_link_wrapper">
      <div class="modal_wrapper my-0 column-buttons">
        <button class="btn btn-warning my-4" id="set_price_target" data-bs-toggle="modal" data-bs-target="#staticBackdrop" onclick="targetMany()">Set Target Prices</button>

        <div class="d-flex justify-content-between align-items-center my-2" style="width: 100%;">
          <button class="btn btn-warning flex-fill me-2" data-bs-toggle="modal" data-bs-target="#baseExpenseModal">Set Base Expense</button>
          <span class="param-value" id="base-expense-display">--</span>
        </div>

        <div class="d-flex justify-content-between align-items-center my-2" style="width: 100%;">
          <button class="btn btn-warning flex-fill me-2" data-bs-toggle="modal" data-bs-target="#multiplicatorModal">Set Multiplicator Value</button>
          <span class="param-value param-multiplicator">--</span>
        </div>

        <div class="d-flex justify-content-between align-items-center my-2" style="width: 100%;">
          <button class="btn btn-warning flex-fill me-2" data-bs-toggle="modal" data-bs-target="#digit_numModal">Set Number of Digit</button>
          <span class="param-value param-digit">--</span>
        </div>

        <div class="d-flex justify-content-between align-items-center my-2" style="width: 100%;">
          <button class="btn btn-warning flex-fill me-2" data-bs-toggle="modal" data-bs-target="#slippage_modal">Set Slippage %</button>
          <span class="param-value param-slippage">--</span>
        </div>

        <div id="reset_btn_wrapper" class="my-3"></div>
      </div>
    </div>
  </div>

  <!-- DESTRA -->
  <div class="col-md-4">
    <div class="header_btn_wrapper my-8 ms-3">
      <!-- Add Crypto -->
      <button class="btn btn-crypto side-button" data-bs-toggle="modal" data-bs-target="#cryptoModal">
        Add New Crypto Pair
      </button>

      <!-- Telegram -->
      <button class="btn btn-telegram side-button" data-bs-toggle="modal" data-bs-target="#telegramConfigModal">
        Configure Telegram Bot
      </button>

      <!-- Export Excel -->
<button id="export_excel_btn" class="btn btn-export side-button">
  Export Excel Table
</button>


<button id="save-sql" class="button-emp button-save-sql">
  SAVE SQL DATABASE
</button>


<input type="file" id="load_sql_input" accept=".sql" style="display: none;" />
<button id="load-sql" class="button-emp button-load-sql">
  LOAD SQL DATABASE
</button>


      <!-- Link utenti/logout -->
      <ul class="menu-column">
        <li><a href="trade.php" class="visually-hidden">Trading Gains</a></li>
        <li><a href="history.php" class="visually-hidden">History Gains</a></li>
        <li>
          <div class="d-flex gap-2">
            <a href="users.php" class="btn btn-user side-button">Users Area</a>
            <a href="logout.php" class="btn btn-logout side-button">Log Out</a>
          </div>
 <!-- IP + Stato Emporion incorniciati -->
<?php
  $externalIp = @file_get_contents('https://api.ipify.org');
  if ($externalIp === false) $externalIp = "Unavailable";
?>
<div class="info-box d-flex flex-column align-items-center justify-content-center">
  <p style="margin: 0;">Your public IP is: <strong><?php echo $externalIp; ?></strong></p>
  <?php
$selected_crypto = $_SESSION['selected_crypto'] ?? null;
$status = $_SESSION['emporion_status'][$selected_crypto] ?? 'closed';
  $class = ($status === 'open') ? 'emporion-open' : 'emporion-closed';
  $text = ucfirst($status);
?>

<p id="emporion_status_text" class="emporion-status text-danger">Emporion is Closed</p>
<!-- RIGA DINAMICA STILE MITOLOGICO -->
<div id="emporion-summary" class="summary-box">
  <div id="summary-line-1" class="summary-line"></div>
  <div id="summary-line-2" class="summary-line"></div>
</div>

</div>
        </li>
      </ul>
    </div>
  </div>
</div>

                <?php 
                    $function = new Functions();

                    $check = $function->checkApi();

                    if (empty($check)) {
                        echo "<div class='alert alert-danger'>
                                <p>API Key or Secret Key is incorrect! Please Log Out and check the API Key or Secret Key</p>
                            </div>";
                        }
    ?>        
</header>


<section id="data-table">
    <div class="container-fluid">
        <table class="table table-striped table-hover text-center">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Order State</th>
                    <th>Order Level</th>
                    <th id="th-suggested-expense">Next Suggested Crypto 2 Expense</th>
<th id="th-crypto2-expense">Crypto 2 Expense</th>
<th id="th-crypto1-variation">Crypto 1 Variation</th>
<th>Target Price</th>
<th>% Price Deviation</th>
<th>Price Deviation</th>
<th id="th-actual-expense">Actual Crypto 2 Expense Value</th>
<th id="th-crypto1-gain">Crypto 1 Gains (50%)</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
    </div>
</section>


<!-- Modal for Target Price -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h1 class="modal-title fs-5" id="staticBackdropLabel">Set Target Prices</h1>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            
            <div class="target_price_wrapper_input row" id="target_price_wrapper_input">
            <!-- <div class="col-md-6 mb-3">
                    <label for="" class="form-label">Level 40</label>
                    <input type="number" id="level40" placeholder="Please enter Target Price" class="form-control" value="0" />
                </div> -->
            </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="modal_close_btn">Close</button>
          <button type="button" class="btn btn-success" onclick="getTargetPrice()">Save</button>
        </div>
      </div>
    </div>
</div>

<!-- modal for add new crypto -->
<div class="modal fade" id="cryptoModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add New Crypto Pair</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
            <form id="add_new_crypto_form">
            <label for="crypto_filter" class="form-label">Filter</label>
<input type="text" id="crypto_filter" class="form-control mb-2" placeholder="Search symbol (e.g. BTCUSDT)" />

<label for="new_crypto_select" class="form-label mt-2">Selection</label>
<select id="new_crypto_select" class="form-select">
    <option disabled selected value="">Select Crypto Pair</option>
</select>



                <input type="submit" value="Add New" class="btn btn-primary mt-4">
            </form>
      </div>
    </div>
  </div>
</div>


<!-- Modal: Telegram Bot Configuration -->
<div class="modal fade" id="telegramConfigModal" tabindex="-1" aria-labelledby="telegramModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content" style="background-color: #fdf6e3; border-radius: 12px;">
      <div class="modal-header">
        <h5 class="modal-title" id="telegramModalLabel">📬 Telegram Bot Configuration</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
      </div>
      <div class="modal-body">
        <h6><strong>🛠 Quick Guide</strong></h6>
        <ol>
          <li>
            <strong>Create a Bot</strong>: Open <a href="https://t.me/BotFather" target="_blank">@BotFather</a>, type <code>/start</code> and then <code>/newbot</code>. Choose a name and username. Copy the token.
          </li>
          <li>
            <strong>Start the Bot</strong>: Search your bot on Telegram, open the chat, click <strong>Start</strong>.
          </li>
          <li>
            <strong>Get your Chat ID</strong>: Open this URL:<br>
            <code>https://api.telegram.org/bot&lt;YOUR_TOKEN&gt;/getUpdates</code><br>
            Replace <code>&lt;YOUR_TOKEN&gt;</code> and find your <code>"chat": { "id": ... }</code>
          </li>
          <li>
            <strong>Fill and Save</strong>: Enter your credentials below and click <strong>Save</strong>.
          </li>
        </ol>

        <hr>

        <form id="telegram-config-form">
          <div class="mb-3">
            <label for="telegram_token" class="form-label"><strong>Telegram Bot Token:</strong></label>
            <input type="text" class="form-control" id="telegram_token" name="telegram_token" placeholder="Enter your bot token" required />
          </div>
          <div class="mb-3">
            <label for="telegram_chat_id" class="form-label"><strong>Chat ID:</strong></label>
            <input type="text" class="form-control" id="telegram_chat_id" name="telegram_chat_id" placeholder="Enter your chat ID" required />
          </div>
          <div class="mb-3">
            <small class="text-muted">
              <strong>Note:</strong> These credentials will be saved for the currently selected crypto pair: <span id="current-crypto-display">None selected</span>
            </small>
          </div>
        </form>

  <p style="font-size: 14px;">
    Le credenziali dei bot Telegram sono salvate direttamente all’inizio del file <code>trade.php</code> nell’array
    <code>$telegram_credentials</code>:
  </p>

<pre style="background:#f4ebdc;border:1px solid #c2a176;padding:10px;border-radius:8px;">
$telegram_credentials = [
    'TAO/BTC' => [
        'chat_id' => '344969533',
        'token' => 'IL_TUO_TOKEN'
    ],
    'EIGEN/BTC' => [
        'chat_id' => '344969533',
        'token' => 'IL_TUO_TOKEN'
    ]
];
</pre>

  <ul style="font-size: 14px;">
    <li>➕ <b>Per aggiungere una nuova coppia:</b> copia una delle righe e modifica la coppia e i valori.</li>
    <li>✏️ <b>Per cambiare token o chat_id:</b> modifica i valori <code>'token'</code> o <code>'chat_id'</code> della coppia interessata.</li>
    <li>🗑️ <b>Per rimuovere una coppia:</b> elimina tutta la sezione corrispondente alla coppia.</li>
  </ul>

  <p style="font-size: 14px;">
    🔄 Dopo aver salvato <code>trade.php</code>, ricarica la pagina: le modifiche saranno subito attive.
  </p>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
  <button type="submit" form="telegram-config-form" class="btn btn-primary">Save Configuration</button>
</div>

    </div>
  </div>
</div>


<!-- Base Expense Modal -->
<div class="modal fade" id="baseExpenseModal" tabindex="-1" aria-labelledby="baseExpenseModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="baseExpenseModalLabel">Set Base Expense</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body px-5">
        <form id="base-expense-form" class="row">
            <input type="number" step="0.00000001" name="base_expense_val" id="base_expense_val" class="form-control my-3" placeholder="Enter base expense (s_bid)"/>
            <input type="hidden" name="crypto" id="current_crypto" value="">

            <button type="submit" class="btn-success">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>


<!-- Multiplicator Modal -->
<div class="modal fade" id="multiplicatorModal" tabindex="-1" aria-labelledby="multiplicatorModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="multiplicatorModalLabel">Multiplicator</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body px-5">
        <form action="includes/multiplicator_cor.php" method="POST" class="row">
            <input type="text" name="multiplicator_val" class="form-control my-3" placeholder="Multiplicator"/>
    <input type="hidden" name="crypto" id="current_crypto" value="">

            <button type="submit" class="btn-success">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- digit modal -->
<div class="modal fade" id="digit_numModal" tabindex="-1" aria-labelledby="digit_numModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="digit_numModalLabel">Digit Number</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body px-5">
        <form action="includes/digit_cor.php" method="POST" class="row">
            <input type="number" name="digit_num_val" class="form-control my-3" placeholder="Digit Number"/>
<input type="hidden" name="crypto" id="current_crypto" value="">

            <button type="submit" class="btn-success">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- slippage modal -->
<div class="modal fade" id="slippage_modal" tabindex="-1" aria-labelledby="slippage_modalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="slippage_modalLabel">Slippage</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body px-5">
        <form action="includes/slippage_cor.php" method="POST" class="row">
            <input type="text" name="slippage_num_val" class="form-control my-3" placeholder="Slippage %" autocomplete="off"/>

            <button type="submit" class="btn-success">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>

<script src="js/action.js"></script>
<script src="js/toast.js"></script>
<script>
function setEmporionStatus(isOpen) {
    const el = document.getElementById("emporion-status-message");
    if (el) {
        el.innerHTML = 'Emporion is <span class="' + (isOpen ? 'emporion-open' : 'emporion-closed') + '">' + (isOpen ? 'Open' : 'Closed') + '</span>';
    }
}
</script>






<script>
function updateEmporionSummary() {
  const rows = document.querySelectorAll("#table-body tr");
  let orders = 0;
  let levels = 0;
  let closestTP = null;
  let closestDiff = Infinity;
  let closestLabel = null;

  const actualPriceEl = document.getElementById("actual_price_value");
  if (!actualPriceEl) return;
  const actualPrice = parseFloat(actualPriceEl.textContent.trim());
  if (isNaN(actualPrice)) return;

  rows.forEach(row => {
    const cells = row.querySelectorAll("td");
    if (cells.length < 7) return;

    const state = cells[1].textContent.trim().toLowerCase();
    const orderLevel = parseInt(cells[2].textContent.trim());
    const tp = parseFloat(cells[6].textContent.trim());
    const label = cells[0].textContent.trim(); // "level46", "level45", ecc.

    if (state === "full") {
      orders++;
      if (!isNaN(orderLevel)) levels += orderLevel;
    }

    if (!isNaN(tp)) {
      const diff = Math.abs(tp - actualPrice);
      if (diff < closestDiff) {
        closestTP = tp;
        closestDiff = diff;
        closestLabel = label;
      }
    }
  });

  const delta = (closestTP !== null && !isNaN(closestTP))
    ? ((closestTP - actualPrice) / actualPrice * 100).toFixed(2)
    : "N/A";

  const formattedTP = (closestTP !== null && !isNaN(closestTP))
    ? closestTP.toFixed(8)
    : "N/A";

  const line1 = `Orders: ${orders}  ✦  Levels: ${levels}`;
  const line2 = `Range: ${closestLabel || "?"}  ✦  TP: ${formattedTP}  ✦  Δ: ${delta}%`;

  document.getElementById("summary-line-1").textContent = line1;
  document.getElementById("summary-line-2").textContent = line2;
}

document.addEventListener("DOMContentLoaded", () => {
  updateEmporionSummary();
  setInterval(updateEmporionSummary, 10000);
});
</script>


<!-- Toast functionality is handled by js/toast.js -->


</body>

<script>
function saveTelegramConfig() {
  const token = document.getElementById("telegram_token").value.trim();
  const chat_id = document.getElementById("telegram_chat_id").value.trim();

  if (!token || !chat_id) {
    alert("Inserisci sia il token che il chat ID.");
    return;
  }

  // ✅ SALVA nei dati del browser
  localStorage.setItem("telegram_token", token);
  localStorage.setItem("telegram_chat_id", chat_id);

  // ✅ Salva anche nel backend
  fetch("./includes/save_telegram_config.php", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ token, chat_id })
  })
  .then(res => res.json())
  .then(data => {
    if (data.status) {
      alert("Configurazione salvata con successo!");
      const modal = bootstrap.Modal.getInstance(document.getElementById("telegramConfigModal"));
      modal.hide();
    } else {
      alert("Errore: " + data.message);
    }
  })
  .catch(err => {
    console.error("Errore durante il salvataggio:", err);
    alert("Errore nella richiesta.");
  });
}
</script>


<script>
document.getElementById('save-sql').addEventListener('click', () => {
    fetch('save_sql.php')
    .then(res => res.text())
    .then(msg => {
        showToast(msg, 'success'); // ✅ usa toast, non alert
    })
    .catch(err => showToast("❌ Errore durante il salvataggio!", 'error'));
});

document.getElementById('load-sql').addEventListener('click', () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.sql';
    input.onchange = e => {
        const file = e.target.files[0];
        if (!file) return;
        const formData = new FormData();
        formData.append('sqlfile', file);

        fetch('load_sql.php', {
            method: 'POST',
            body: formData
        })
        .then(res => res.text())   // 🔄 ora legge testo, non JSON
        .then(msg => {
            showToast(msg, 'success');
            setTimeout(() => location.reload(), 2000); // ricarica dopo 2s
        })
        .catch(err => showToast("❌ Errore durante il caricamento!", 'error'));
    };
    input.click();
});
</script>



</html>