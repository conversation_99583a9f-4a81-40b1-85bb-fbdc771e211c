-- Migration script to add missing fields to crypto table
-- Run this script if you have an existing crypto_bot database

-- Add missing columns to crypto table
ALTER TABLE `crypto` 
ADD COLUMN `s_bid` decimal(19,10) DEFAULT 0.00000001,
ADD COLUMN `multiplicator` decimal(10,8) DEFAULT 1.02000000,
ADD COLUMN `digit_num` int(11) DEFAULT 8,
ADD COLUMN `slippage_num` decimal(10,8) DEFAULT 0.01000000,
ADD COLUMN `telegram_token` varchar(512) DEFAULT NULL,
ADD COLUMN `telegram_chat_id` varchar(256) DEFAULT NULL;

-- Update existing records with default values if they don't have them
UPDATE `crypto` SET 
    `s_bid` = 0.00000001 WHERE `s_bid` IS NULL,
    `multiplicator` = 1.02000000 WHERE `multiplicator` IS NULL,
    `digit_num` = 8 WHERE `digit_num` IS NULL,
    `slippage_num` = 0.01000000 WHERE `slippage_num` IS NULL;
