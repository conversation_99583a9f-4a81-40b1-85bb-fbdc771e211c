<?php
session_start();
include "conn.php";

$data = json_decode(file_get_contents("php://input"), true);
$crypto = $data["crypto"];

if (!$crypto) {
    echo json_encode(["status" => false, "message" => "Crypto pair is required"]);
    exit;
}

// Get telegram credentials from the crypto table
$sql = "SELECT telegram_token, telegram_chat_id FROM crypto WHERE crypto_name = ? AND user = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("si", $crypto, $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();

if ($row = $result->fetch_assoc()) {
    echo json_encode([
        "status" => true,
        "telegram_token" => $row['telegram_token'],
        "telegram_chat_id" => $row['telegram_chat_id']
    ]);
} else {
    echo json_encode([
        "status" => false,
        "message" => "No telegram credentials found for this crypto pair"
    ]);
}

$stmt->close();
$conn->close();
?>
